"""
测试前端适配API的脚本
"""
import requests
import json
import time
from typing import Dict, Any

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

class FrontendAPITester:
    """前端API测试类"""
    
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.timeout = 30
        self.access_token = None
        
    def test_auth_login(self):
        """测试登录API（form-data格式）"""
        print("\n🧪 测试用户登录（form-data格式）...")
        
        # 使用form-data格式登录
        login_data = {
            "username": "test_user",
            "password": "test_password123"
        }
        
        response = self.session.post(f"{self.base_url}/auth/login", data=login_data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 登录成功: {result['message']}")
            
            # 检查响应格式
            if "code" in result and "data" in result and "timestamp" in result:
                print("✅ 响应格式符合前端要求")
                self.access_token = result["data"]["access_token"]
                
                # 设置认证头
                self.session.headers.update({
                    "Authorization": f"Bearer {self.access_token}"
                })
                return True
            else:
                print("❌ 响应格式不符合前端要求")
                return False
        else:
            print(f"❌ 登录失败: {response.status_code} - {response.text}")
            return False
    
    def test_user_me(self):
        """测试获取用户信息API"""
        print("\n🧪 测试获取用户信息...")
        
        response = self.session.get(f"{self.base_url}/users/me")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 获取用户信息成功: {result['message']}")
            
            # 检查响应格式
            if "code" in result and "data" in result:
                user_data = result["data"]
                print(f"   用户ID: {user_data.get('id')}")
                print(f"   用户名: {user_data.get('username')}")
                print(f"   邮箱: {user_data.get('email')}")
                return True
            else:
                print("❌ 响应格式不符合前端要求")
                return False
        else:
            print(f"❌ 获取用户信息失败: {response.status_code}")
            return False
    
    def test_ai_models(self):
        """测试AI模型配置API"""
        print("\n🧪 测试AI模型配置...")
        
        # 1. 创建AI模型配置
        print("\n1. 创建AI模型配置")
        model_data = {
            "name": "测试模型",
            "provider": "openai",
            "base_url": "https://api.openai.com/v1",
            "api_key": "sk-test",
            "model": "gpt-3.5-turbo",
            "max_tokens": 4096,
            "temperature": 0.7,
            "is_default": True,
            "is_active": True
        }
        
        response = self.session.post(f"{self.base_url}/ai/models", json=model_data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ AI模型创建成功: {result['message']}")
            model_id = result["data"]["id"]
        else:
            print(f"❌ AI模型创建失败: {response.status_code} - {response.text}")
            return None
        
        # 2. 获取AI模型列表
        print("\n2. 获取AI模型列表")
        response = self.session.get(f"{self.base_url}/ai/models")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 获取AI模型列表成功: 共{len(result['data'])}个模型")
        else:
            print(f"❌ 获取AI模型列表失败: {response.status_code}")
        
        return model_id
    
    def test_conversations(self, model_id):
        """测试AI对话API"""
        print("\n🧪 测试AI对话...")
        
        # 1. 创建对话
        print("\n1. 创建对话")
        conversation_data = {
            "title": "测试对话",
            "model_config_id": model_id
        }
        
        response = self.session.post(f"{self.base_url}/ai/conversations", json=conversation_data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 对话创建成功: {result['message']}")
            conversation_id = result["data"]["id"]
        else:
            print(f"❌ 对话创建失败: {response.status_code} - {response.text}")
            return None
        
        # 2. 获取对话列表
        print("\n2. 获取对话列表")
        response = self.session.get(f"{self.base_url}/ai/conversations")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 获取对话列表成功: 共{len(result['data'])}个对话")
        else:
            print(f"❌ 获取对话列表失败: {response.status_code}")
        
        return conversation_id
    
    def test_prompts(self):
        """测试AI Prompt模板API"""
        print("\n🧪 测试AI Prompt模板...")
        
        # 1. 创建Prompt模板
        print("\n1. 创建Prompt模板")
        prompt_data = {
            "name": "测试Prompt",
            "identifier": "test_prompt",
            "content": "你是一个AI助手，请回答：{{question}}",
            "description": "测试用的Prompt模板",
            "category": "general",
            "variables": ["question"],
            "is_active": True
        }
        
        response = self.session.post(f"{self.base_url}/ai/prompts", json=prompt_data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Prompt模板创建成功: {result['message']}")
            prompt_id = result["data"]["id"]
        else:
            print(f"❌ Prompt模板创建失败: {response.status_code} - {response.text}")
            return None
        
        # 2. 验证Prompt模板
        print("\n2. 验证Prompt模板")
        validation_data = {
            "content": "你是一个AI助手，请回答：{{question}}",
            "variables": {
                "question": "什么是人工智能？"
            }
        }
        
        response = self.session.post(f"{self.base_url}/ai/prompts/validate", json=validation_data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Prompt验证成功: 有效={result['data']['is_valid']}")
        else:
            print(f"❌ Prompt验证失败: {response.status_code}")
        
        return prompt_id
    
    def test_requirements(self):
        """测试需求管理API"""
        print("\n🧪 测试需求管理...")
        
        # 1. 创建需求
        print("\n1. 创建需求")
        requirement_data = {
            "name": "测试需求",
            "input_type": "text",
            "original_content": "开发一个用户登录功能，支持用户名密码登录和邮箱登录",
            "category": "authentication",
            "priority": "high"
        }
        
        response = self.session.post(f"{self.base_url}/requirements", json=requirement_data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 需求创建成功: {result['message']}")
            requirement_id = result["data"]["id"]
        else:
            print(f"❌ 需求创建失败: {response.status_code} - {response.text}")
            return None
        
        # 2. 获取需求列表
        print("\n2. 获取需求列表")
        response = self.session.get(f"{self.base_url}/requirements")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 获取需求列表成功: 共{len(result['data'])}个需求")
        else:
            print(f"❌ 获取需求列表失败: {response.status_code}")
        
        return requirement_id
    
    def test_dashboard(self):
        """测试仪表盘API"""
        print("\n🧪 测试仪表盘...")
        
        # 1. 获取仪表盘统计
        print("\n1. 获取仪表盘统计")
        response = self.session.get(f"{self.base_url}/dashboard/stats")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 获取仪表盘统计成功: {result['message']}")
            stats = result["data"]
            print(f"   总任务数: {stats.get('total_tasks')}")
            print(f"   已完成任务: {stats.get('completed_tasks')}")
            print(f"   运行中任务: {stats.get('running_tasks')}")
            print(f"   失败任务: {stats.get('failed_tasks')}")
        else:
            print(f"❌ 获取仪表盘统计失败: {response.status_code}")
        
        # 2. 获取图表数据
        print("\n2. 获取图表数据")
        response = self.session.get(f"{self.base_url}/dashboard/stats/charts")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 获取图表数据成功: {result['message']}")
        else:
            print(f"❌ 获取图表数据失败: {response.status_code}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始前端适配API测试...")
        
        try:
            # 测试登录
            if not self.test_auth_login():
                print("❌ 登录测试失败，跳过后续测试")
                return
            
            # 测试用户信息
            self.test_user_me()
            
            # 测试AI模型配置
            model_id = self.test_ai_models()
            
            # 测试AI对话
            if model_id:
                conversation_id = self.test_conversations(model_id)
            
            # 测试Prompt模板
            prompt_id = self.test_prompts()
            
            # 测试需求管理
            requirement_id = self.test_requirements()
            
            # 测试仪表盘
            self.test_dashboard()
            
            print(f"\n🎉 所有前端适配API测试完成!")
            
        except Exception as e:
            print(f"\n❌ 测试过程中出现错误: {e}")


def main():
    """主函数"""
    print("=" * 60)
    print("AI研发辅助平台 - 前端适配API测试")
    print("=" * 60)
    
    tester = FrontendAPITester()
    
    # 检查服务是否运行
    try:
        response = requests.get(f"{BASE_URL.replace('/api/v1', '')}/health", timeout=10)
        if response.status_code == 200:
            print("✅ 服务运行正常")
            tester.run_all_tests()
        else:
            print(f"❌ 服务响应异常: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到服务: {e}")
        print("请确保服务已启动: python -m uvicorn app.main:app --reload")


if __name__ == "__main__":
    main()
