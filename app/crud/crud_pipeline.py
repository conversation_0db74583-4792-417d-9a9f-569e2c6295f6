"""
流水线相关CRUD操作
"""
import uuid
from datetime import datetime
from typing import Optional, List
from sqlmodel import Session, select, func
from app.crud.base import CRUDBase
from app.models.pipeline import CodeDiff, RequirementText
from app.models.task import PipelineTask, TaskExecution
from app.schemas.pipeline import (
    CodeDiffCreate, RequirementTextCreate, RequirementTextUpdate,
    PipelineTaskCreate, PipelineTaskUpdate
)


class CRUDCodeDiff(CRUDBase[CodeDiff, CodeDiffCreate, dict]):
    """代码差异CRUD操作"""
    
    def get_by_repository(self, db: Session, *, repository_id: int) -> List[CodeDiff]:
        """获取仓库的所有代码差异"""
        statement = select(CodeDiff).where(CodeDiff.repository_id == repository_id)
        return db.exec(statement).all()
    
    def get_by_status(self, db: Session, *, status: str) -> List[CodeDiff]:
        """根据状态获取代码差异"""
        statement = select(CodeDiff).where(CodeDiff.status == status)
        return db.exec(statement).all()
    
    def update_status(
        self, 
        db: Session, 
        *, 
        diff_id: int, 
        status: str, 
        diff_file_path: str = None,
        error_message: str = None
    ) -> Optional[CodeDiff]:
        """更新代码差异状态"""
        diff = self.get(db, diff_id)
        if diff:
            diff.status = status
            if diff_file_path:
                diff.diff_file_path = diff_file_path
            if error_message:
                diff.error_message = error_message
            db.add(diff)
            db.commit()
            db.refresh(diff)
        return diff
    
    def get_by_refs(
        self, 
        db: Session, 
        *, 
        repository_id: int, 
        base_ref: str, 
        head_ref: str
    ) -> Optional[CodeDiff]:
        """根据仓库和分支获取代码差异"""
        statement = select(CodeDiff).where(
            CodeDiff.repository_id == repository_id,
            CodeDiff.base_ref == base_ref,
            CodeDiff.head_ref == head_ref
        )
        return db.exec(statement).first()


class CRUDRequirementText(CRUDBase[RequirementText, RequirementTextCreate, RequirementTextUpdate]):
    """需求文本CRUD操作"""
    
    def get_by_category(self, db: Session, *, category: str) -> List[RequirementText]:
        """根据分类获取需求文本"""
        statement = select(RequirementText).where(RequirementText.category == category)
        return db.exec(statement).all()
    
    def get_by_status(self, db: Session, *, status: str) -> List[RequirementText]:
        """根据状态获取需求文本"""
        statement = select(RequirementText).where(RequirementText.status == status)
        return db.exec(statement).all()
    
    def get_by_priority(self, db: Session, *, priority: str) -> List[RequirementText]:
        """根据优先级获取需求文本"""
        statement = select(RequirementText).where(RequirementText.priority == priority)
        return db.exec(statement).all()
    
    def search_by_title(self, db: Session, *, title_query: str) -> List[RequirementText]:
        """根据标题搜索需求文本"""
        statement = select(RequirementText).where(
            RequirementText.title.contains(title_query)
        )
        return db.exec(statement).all()
    
    def get_categories(self, db: Session) -> List[str]:
        """获取所有分类"""
        statement = select(RequirementText.category).distinct()
        results = db.exec(statement).all()
        return [c for c in results if c is not None]


class CRUDPipelineTask(CRUDBase[PipelineTask, PipelineTaskCreate, PipelineTaskUpdate]):
    """流水线任务CRUD操作"""
    
    def get_by_type(self, db: Session, *, pipeline_type: str) -> List[PipelineTask]:
        """根据流水线类型获取任务"""
        statement = select(PipelineTask).where(PipelineTask.pipeline_type == pipeline_type)
        return db.exec(statement).all()
    
    def get_by_status(self, db: Session, *, status: str) -> List[PipelineTask]:
        """根据状态获取任务"""
        statement = select(PipelineTask).where(PipelineTask.status == status)
        return db.exec(statement).all()
    
    def get_by_code_diff(self, db: Session, *, code_diff_id: int) -> List[PipelineTask]:
        """根据代码差异获取任务"""
        statement = select(PipelineTask).where(PipelineTask.code_diff_id == code_diff_id)
        return db.exec(statement).all()
    
    def get_by_requirement(self, db: Session, *, requirement_text_id: int) -> List[PipelineTask]:
        """根据需求文本获取任务"""
        statement = select(PipelineTask).where(PipelineTask.requirement_text_id == requirement_text_id)
        return db.exec(statement).all()
    
    def update_status(
        self, 
        db: Session, 
        *, 
        task_id: int, 
        status: str,
        result: str = None,
        error_message: str = None,
        execution_time: float = None
    ) -> Optional[PipelineTask]:
        """更新任务状态"""
        task = self.get(db, task_id)
        if task:
            task.status = status
            if result:
                task.result = result
            if error_message:
                task.error_message = error_message
            if execution_time:
                task.execution_time = execution_time
            db.add(task)
            db.commit()
            db.refresh(task)
        return task
    
    def get_stats(self, db: Session) -> dict:
        """获取任务统计信息"""
        # 总任务数
        total_tasks = db.exec(select(func.count(PipelineTask.id))).first()
        
        # 各状态任务数
        pending_tasks = db.exec(
            select(func.count(PipelineTask.id)).where(PipelineTask.status == "pending")
        ).first()
        
        running_tasks = db.exec(
            select(func.count(PipelineTask.id)).where(PipelineTask.status.in_(["queued", "running"]))
        ).first()
        
        completed_tasks = db.exec(
            select(func.count(PipelineTask.id)).where(PipelineTask.status == "completed")
        ).first()
        
        failed_tasks = db.exec(
            select(func.count(PipelineTask.id)).where(PipelineTask.status == "failed")
        ).first()
        
        # 平均执行时间
        avg_execution_time = db.exec(
            select(func.avg(PipelineTask.execution_time)).where(
                PipelineTask.status == "completed",
                PipelineTask.execution_time.is_not(None)
            )
        ).first()
        
        # 成功率
        success_rate = 0.0
        if total_tasks and total_tasks > 0:
            success_rate = (completed_tasks or 0) / total_tasks * 100
        
        return {
            "total_tasks": total_tasks or 0,
            "pending_tasks": pending_tasks or 0,
            "running_tasks": running_tasks or 0,
            "completed_tasks": completed_tasks or 0,
            "failed_tasks": failed_tasks or 0,
            "avg_execution_time": avg_execution_time,
            "success_rate": success_rate
        }


class CRUDTaskExecution(CRUDBase[TaskExecution, dict, dict]):
    """任务执行CRUD操作"""
    
    def create_execution(
        self, 
        db: Session, 
        *, 
        task_id: int,
        steps_total: int = 0
    ) -> TaskExecution:
        """创建任务执行记录"""
        execution_id = f"exec_{uuid.uuid4()}"
        
        execution = TaskExecution(
            task_id=task_id,
            execution_id=execution_id,
            status="running",
            steps_total=steps_total,
            steps_completed=0
        )
        
        db.add(execution)
        db.commit()
        db.refresh(execution)
        return execution
    
    def get_by_execution_id(self, db: Session, *, execution_id: str) -> Optional[TaskExecution]:
        """根据执行ID获取执行记录"""
        statement = select(TaskExecution).where(TaskExecution.execution_id == execution_id)
        return db.exec(statement).first()
    
    def get_by_task(self, db: Session, *, task_id: int) -> List[TaskExecution]:
        """获取任务的所有执行记录"""
        statement = select(TaskExecution).where(TaskExecution.task_id == task_id)
        return db.exec(statement).all()
    
    def update_progress(
        self, 
        db: Session, 
        *, 
        execution_id: str,
        steps_completed: int,
        current_step: str = None,
        logs: str = None
    ) -> Optional[TaskExecution]:
        """更新执行进度"""
        execution = self.get_by_execution_id(db, execution_id=execution_id)
        if execution:
            execution.steps_completed = steps_completed
            if current_step:
                execution.current_step = current_step
            if logs:
                execution.logs = logs
            db.add(execution)
            db.commit()
            db.refresh(execution)
        return execution
    
    def complete_execution(
        self, 
        db: Session, 
        *, 
        execution_id: str,
        status: str,
        result: str = None,
        error_message: str = None,
        resources_used: dict = None
    ) -> Optional[TaskExecution]:
        """完成任务执行"""
        execution = self.get_by_execution_id(db, execution_id=execution_id)
        if execution:
            execution.status = status
            execution.completed_at = str(datetime.utcnow())
            if result:
                execution.result = result
            if error_message:
                execution.error_message = error_message
            if resources_used:
                execution.resources_used = resources_used
            db.add(execution)
            db.commit()
            db.refresh(execution)
        return execution


# 创建CRUD实例
code_diff = CRUDCodeDiff(CodeDiff)
requirement_text = CRUDRequirementText(RequirementText)
pipeline_task = CRUDPipelineTask(PipelineTask)
task_execution = CRUDTaskExecution(TaskExecution)
