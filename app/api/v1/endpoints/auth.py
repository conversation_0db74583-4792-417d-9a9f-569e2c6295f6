"""
用户认证相关API端点
"""
from datetime import <PERSON><PERSON><PERSON>
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from sqlmodel import Session
from app.api.v1.deps import get_db
from app.crud.crud_user import user, user_session, user_login_log
from app.schemas.user import (
    UserRegister, UserLogin, UserLoginResponse, UserResponse,
    UserUpdate, UserPreferencesUpdate, PasswordChange,
    UserSessionResponse, RefreshTokenRequest, UserLoginLogResponse,
    UserStats, MessageResponse
)
from app.core.security import create_access_token, verify_token
from app.core.config import settings

router = APIRouter()
security = HTTPBearer()


def get_client_info(request: Request) -> dict:
    """获取客户端信息"""
    return {
        "ip_address": request.client.host if request.client else None,
        "user_agent": request.headers.get("user-agent"),
        "device_info": request.headers.get("user-agent", "Unknown")[:100]
    }


def get_current_user_id(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> str:
    """获取当前用户ID"""
    token = credentials.credentials
    user_id = verify_token(token)
    
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user_id


def get_current_user(
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
) -> UserResponse:
    """获取当前用户信息"""
    current_user = user.get(db, int(user_id))
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户已被禁用"
        )
    return current_user


# ===== 认证相关端点 =====
@router.post("/register", response_model=UserResponse)
def register(
    *,
    db: Session = Depends(get_db),
    user_in: UserRegister,
    request: Request
):
    """用户注册"""
    try:
        new_user = user.create(db=db, obj_in=user_in)
        
        # 记录注册日志
        client_info = get_client_info(request)
        user_login_log.create_log(
            db=db,
            user_id=new_user.id,
            login_type="register",
            success=True,
            **client_info
        )
        
        return new_user
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"注册失败: {str(e)}"
        )


@router.post("/login")
def login(
    *,
    db: Session = Depends(get_db),
    username: str = Form(...),
    password: str = Form(...),
    request: Request
):
    """用户登录"""
    client_info = get_client_info(request)
    
    # 验证用户
    authenticated_user = user.authenticate(
        db, username=login_data.username, password=login_data.password
    )
    
    if not authenticated_user:
        # 记录失败日志
        user_login_log.create_log(
            db=db,
            user_id=0,  # 未知用户
            login_type="password",
            success=False,
            failure_reason="用户名或密码错误",
            **client_info
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误"
        )
    
    if not authenticated_user.is_active:
        user_login_log.create_log(
            db=db,
            user_id=authenticated_user.id,
            login_type="password",
            success=False,
            failure_reason="用户已被禁用",
            **client_info
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户已被禁用"
        )
    
    # 创建访问令牌
    expires_delta = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    if login_data.remember_me:
        expires_delta = timedelta(days=30)  # 记住我30天
    
    access_token = create_access_token(
        subject=str(authenticated_user.id), expires_delta=expires_delta
    )
    
    # 创建刷新令牌
    refresh_token = create_access_token(
        subject=str(authenticated_user.id), 
        expires_delta=timedelta(days=30)
    )
    
    # 创建会话
    session = user_session.create_session(
        db=db,
        user_id=authenticated_user.id,
        access_token=access_token,
        refresh_token=refresh_token,
        expires_in=int(expires_delta.total_seconds()),
        device_info=login_data.device_info or client_info["device_info"],
        **client_info
    )
    
    # 更新用户登录信息
    user.update_login_info(db, user_id=authenticated_user.id)
    
    # 记录成功日志
    user_login_log.create_log(
        db=db,
        user_id=authenticated_user.id,
        login_type="password",
        success=True,
        **client_info
    )
    
    return UserLoginResponse(
        access_token=access_token,
        refresh_token=refresh_token,
        expires_in=int(expires_delta.total_seconds()),
        user=authenticated_user
    )


@router.post("/refresh", response_model=UserLoginResponse)
def refresh_token(
    *,
    db: Session = Depends(get_db),
    refresh_data: RefreshTokenRequest,
    request: Request
):
    """刷新访问令牌"""
    # 验证刷新令牌
    session = user_session.get_by_refresh_token(db, refresh_token=refresh_data.refresh_token)
    if not session:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的刷新令牌"
        )
    
    # 获取用户
    current_user = user.get(db, session.user_id)
    if not current_user or not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在或已被禁用"
        )
    
    # 创建新的访问令牌
    expires_delta = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    new_access_token = create_access_token(
        subject=str(current_user.id), expires_delta=expires_delta
    )
    
    # 更新会话
    session.session_token = new_access_token
    db.add(session)
    db.commit()
    
    return UserLoginResponse(
        access_token=new_access_token,
        refresh_token=refresh_data.refresh_token,
        expires_in=int(expires_delta.total_seconds()),
        user=current_user
    )


@router.post("/logout", response_model=MessageResponse)
def logout(
    *,
    db: Session = Depends(get_db),
    current_user: UserResponse = Depends(get_current_user),
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """用户登出"""
    # 撤销当前会话
    session = user_session.get_by_token(db, token=credentials.credentials)
    if session:
        user_session.revoke_session(db, session_id=session.id)
    
    return MessageResponse(message="登出成功")


@router.post("/logout-all", response_model=MessageResponse)
def logout_all(
    *,
    db: Session = Depends(get_db),
    current_user: UserResponse = Depends(get_current_user)
):
    """登出所有设备"""
    # 撤销用户的所有会话
    revoked_count = user_session.revoke_user_sessions(db, user_id=current_user.id)
    
    return MessageResponse(message=f"已登出 {revoked_count} 个设备")


# ===== 用户信息管理 =====
@router.get("/me", response_model=UserResponse)
def read_current_user(
    current_user: UserResponse = Depends(get_current_user)
):
    """获取当前用户信息"""
    return current_user


@router.put("/me", response_model=UserResponse)
def update_current_user(
    *,
    db: Session = Depends(get_db),
    user_in: UserUpdate,
    current_user: UserResponse = Depends(get_current_user)
):
    """更新当前用户信息"""
    try:
        updated_user = user.update(db=db, db_obj=current_user, obj_in=user_in)
        return updated_user
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新用户信息失败: {str(e)}"
        )


@router.put("/me/preferences", response_model=UserResponse)
def update_user_preferences(
    *,
    db: Session = Depends(get_db),
    preferences: UserPreferencesUpdate,
    current_user: UserResponse = Depends(get_current_user)
):
    """更新用户偏好设置"""
    try:
        updated_user = user.update_preferences(
            db=db, user_id=current_user.id, preferences=preferences
        )
        return updated_user
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新偏好设置失败: {str(e)}"
        )


@router.post("/me/change-password", response_model=MessageResponse)
def change_password(
    *,
    db: Session = Depends(get_db),
    password_data: PasswordChange,
    current_user: UserResponse = Depends(get_current_user)
):
    """修改密码"""
    # 验证新密码确认
    if password_data.new_password != password_data.confirm_password:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="新密码与确认密码不匹配"
        )
    
    # 修改密码
    success = user.change_password(
        db=db,
        user_id=current_user.id,
        current_password=password_data.current_password,
        new_password=password_data.new_password
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="当前密码错误"
        )
    
    # 撤销所有会话，要求重新登录
    user_session.revoke_user_sessions(db, user_id=current_user.id)
    
    return MessageResponse(message="密码修改成功，请重新登录")


# ===== 会话管理 =====
@router.get("/me/sessions", response_model=List[UserSessionResponse])
def read_user_sessions(
    *,
    db: Session = Depends(get_db),
    current_user: UserResponse = Depends(get_current_user)
):
    """获取用户会话列表"""
    sessions = user_session.get_user_sessions(db, user_id=current_user.id)
    return sessions


@router.delete("/me/sessions/{session_id}", response_model=MessageResponse)
def revoke_session(
    *,
    db: Session = Depends(get_db),
    session_id: int,
    current_user: UserResponse = Depends(get_current_user)
):
    """撤销指定会话"""
    # 验证会话属于当前用户
    session = user_session.get(db, session_id)
    if not session or session.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="会话不存在"
        )
    
    success = user_session.revoke_session(db, session_id=session_id)
    if success:
        return MessageResponse(message="会话已撤销")
    else:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="撤销会话失败"
        )


# ===== 登录日志 =====
@router.get("/me/login-logs", response_model=List[UserLoginLogResponse])
def read_login_logs(
    *,
    db: Session = Depends(get_db),
    current_user: UserResponse = Depends(get_current_user),
    limit: int = 50
):
    """获取登录日志"""
    logs = user_login_log.get_user_logs(db, user_id=current_user.id, limit=limit)
    return logs
